<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WhatsApp Testing Dashboard</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      button {
        background: #25d366;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }
      button:hover {
        background: #128c7e;
      }
      button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      .danger {
        background: #dc3545;
      }
      .danger:hover {
        background: #c82333;
      }
      .info {
        background: #17a2b8;
      }
      .info:hover {
        background: #138496;
      }
      .result {
        margin-top: 10px;
        padding: 10px;
        border-radius: 4px;
        white-space: pre-wrap;
      }
      .success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      input,
      textarea {
        width: 100%;
        padding: 8px;
        margin: 5px 0;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
      textarea {
        height: 100px;
        resize: vertical;
      }
    </style>
  </head>
  <body>
    <h1>🚀 WhatsApp Testing Dashboard</h1>

    <div class="container">
      <h2>📊 WhatsApp Status</h2>
      <button onclick="checkStatus()">Check WhatsApp Status</button>
      <div id="statusResult" class="result"></div>
    </div>

    <div class="container">
      <h2>🔄 Session Management</h2>
      <button class="danger" onclick="resetWhatsAppSession()">
        🚨 Reset WhatsApp Session (Fix Conflicts)
      </button>
      <div id="resetResult" class="result"></div>
    </div>

    <div class="container">
      <h2>🧹 Queue Management</h2>
      <button class="danger" onclick="clearQueue()">
        Clear All Queued Messages
      </button>
      <button class="info" onclick="processQueue()">
        Process Queue Manually
      </button>
      <div id="queueResult" class="result"></div>
    </div>

    <div class="container">
      <h2>📱 Direct Message Test</h2>
      <input
        type="text"
        id="phoneNumber"
        placeholder="Phone number (e.g., 9876543210)"
      />
      <textarea id="messageText" placeholder="Test message...">
🧪 This is a test message from WhatsApp bot!</textarea
      >
      <button onclick="sendDirectMessage()">Send Direct Message</button>
      <div id="messageResult" class="result"></div>
    </div>

    <script>
      const SUPABASE_URL = "https://rnqwdwqhqxjqkqvkwqkw.supabase.co";
      const SALON_ID = "d0a56955-eba7-4679-b0de-c9946505c1af";
      const BOT_URL = "https://wb-userbot-production.up.railway.app";

      async function checkStatus() {
        const resultDiv = document.getElementById("statusResult");
        resultDiv.textContent = "Checking status...";

        try {
          const response = await fetch(`${BOT_URL}/session/${SALON_ID}/status`);
          const result = await response.json();

          resultDiv.className = "result success";
          resultDiv.textContent = JSON.stringify(result, null, 2);
        } catch (error) {
          resultDiv.className = "result error";
          resultDiv.textContent = `Error: ${error.message}`;
        }
      }

      async function clearQueue() {
        const resultDiv = document.getElementById("queueResult");
        resultDiv.textContent = "Clearing queue...";

        try {
          const response = await fetch(
            `${SUPABASE_URL}/functions/v1/clear-whatsapp-queue`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
            }
          );
          const result = await response.json();

          resultDiv.className = "result success";
          resultDiv.textContent = JSON.stringify(result, null, 2);
        } catch (error) {
          resultDiv.className = "result error";
          resultDiv.textContent = `Error: ${error.message}`;
        }
      }

      async function processQueue() {
        const resultDiv = document.getElementById("queueResult");
        resultDiv.textContent = "Processing queue...";

        try {
          const response = await fetch(
            `${SUPABASE_URL}/functions/v1/manual-queue-processor`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
            }
          );
          const result = await response.json();

          resultDiv.className = "result success";
          resultDiv.textContent = JSON.stringify(result, null, 2);
        } catch (error) {
          resultDiv.className = "result error";
          resultDiv.textContent = `Error: ${error.message}`;
        }
      }

      async function sendDirectMessage() {
        const phoneNumber = document.getElementById("phoneNumber").value;
        const message = document.getElementById("messageText").value;
        const resultDiv = document.getElementById("messageResult");

        if (!phoneNumber || !message) {
          resultDiv.className = "result error";
          resultDiv.textContent = "Please enter both phone number and message";
          return;
        }

        resultDiv.textContent = "Sending message...";

        try {
          // Format phone number
          let formattedPhone = phoneNumber.replace(/\D/g, "");
          if (
            !formattedPhone.startsWith("91") &&
            formattedPhone.length === 10
          ) {
            formattedPhone = "91" + formattedPhone;
          }

          const response = await fetch(`${BOT_URL}/session/${SALON_ID}/send`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              phoneNumber: formattedPhone,
              message: message,
            }),
          });
          const result = await response.json();

          if (response.ok) {
            resultDiv.className = "result success";
            resultDiv.textContent = `✅ Message sent successfully!\n\n${JSON.stringify(
              result,
              null,
              2
            )}`;
          } else {
            resultDiv.className = "result error";
            resultDiv.textContent = `❌ Failed to send message:\n\n${JSON.stringify(
              result,
              null,
              2
            )}`;
          }
        } catch (error) {
          resultDiv.className = "result error";
          resultDiv.textContent = `Error: ${error.message}`;
        }
      }

      // Auto-check status on page load
      window.onload = function () {
        checkStatus();
      };
    </script>
  </body>
</html>
