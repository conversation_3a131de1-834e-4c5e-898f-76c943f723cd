export interface User {
  user_id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  role: 'owner' | 'staff';
  created_at: string;
  updated_at: string;
  avatar_url?: string;
  display_name?: string;
  location?: string;
  date_of_birth?: string;
  branch?: string;
  employee_id?: string;
  working_hours?: string;
  joined_date?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  salon_id?: string;
  is_active?: boolean;
}

export interface Salon {
  salon_id: string;
  owner_user_id: string;
  name: string;
  address?: string;
  created_at: string;
  updated_at: string;
}

export interface Client {
  client_id: string;
  salon_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  email?: string;
  address?: string;
  created_at: string;
  updated_at: string;
  is_active?: boolean;
}

export interface Appointment {
  appointment_id: string;
  salon_id: string;
  client_id: string;
  appointment_date: string;
  duration?: number;
  status: 'scheduled' | 'checked-in' | 'completed' | 'cancelled' | 'no_show';
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface AppointmentService {
  id: string;
  appointment_id: string;
  service_id: string;
  price: number;
  created_at: string;
  updated_at: string;
}

export interface Service {
  service_id: string;
  salon_id: string;
  name: string;
  description?: string;
  price: number;
  duration?: number;
  created_at: string;
  updated_at: string;
  is_active?: boolean;
}

export interface Transaction {
  transaction_id: string;
  salon_id: string;
  appointment_id?: string;
  client_id?: string;
  user_id: string;
  total_amount: number;
  payment_method: 'cash' | 'card' | 'upi';
  status: 'pending' | 'completed' | 'failed';
  transaction_date: string;
  payment_reference?: string;
  created_at: string;
  updated_at: string;
}

export interface TransactionItem {
  item_id: string;
  transaction_id: string;
  item_type: 'service' | 'product';
  reference_id: string;
  description?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  created_at: string;
  updated_at: string;
}

export interface Database {
  Users: User;
  Salons: Salon;
  Clients: Client;
  Appointments: Appointment;
  Appointment_Services: AppointmentService;
  Services: Service;
  Transactions: Transaction;
  Transaction_Items: TransactionItem;
}
