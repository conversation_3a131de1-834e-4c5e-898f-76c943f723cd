// Adapted from shadcn/ui (https://ui.shadcn.com/docs/components/toast)
import { useState, useEffect, useCallback } from "react";

export type ToastProps = {
  title?: string;
  description?: string;
  action?: React.ReactNode;
  variant?: "default" | "destructive";
};

export type Toast = {
  id: string;
  props: ToastProps;
  onDismiss: () => void;
};

let toastCount = 0;

export function useToast() {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const dismiss = useCallback((id: string) => {
    setToasts((toasts) => toasts.filter((toast) => toast.id !== id));
  }, []);

  const toast = useCallback(
    ({ ...props }: ToastProps) => {
      const id = `toast-${toastCount++}`;
      const newToast: Toast = {
        id,
        props,
        onDismiss: () => dismiss(id),
      };

      setToasts((toasts) => [...toasts, newToast]);

      // Auto-dismiss after 5 seconds
      setTimeout(() => {
        dismiss(id);
      }, 5000);

      return id;
    },
    [dismiss]
  );

  return {
    toast,
    toasts,
    dismiss,
  };
}

export type ToastActionElement = React.ReactElement<{
  altText: string;
  onClick: () => void;
}>;
