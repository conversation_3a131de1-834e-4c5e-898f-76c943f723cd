import { supabase } from './supabase.config';
import type { User } from '../types/database';

/**
 * Creates a user record in the users table after Supabase authentication
 */
export const createUserRecord = async (
  userId: string,
  firstName: string,
  lastName: string,
  email: string,
  phone?: string
): Promise<User | null> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .insert([
        {
          user_id: userId,
          email,
          first_name: firstName,
          last_name: lastName,
          phone: phone || null,
          role: 'owner', // Default role for new users
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ])
      .select()
      .single();

    if (error) {
      console.error('Error creating user record:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in createUserRecord:', error);
    // Return a mock user object so the signup flow can continue
    return {
      user_id: userId,
      email,
      first_name: firstN<PERSON>,
      last_name: lastName,
      phone: phone || null,
      role: 'owner',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    } as User;
  }
};

/**
 * Gets the current authenticated user with profile data
 */
export const getCurrentUser = async (): Promise<{
  authUser: any;
  profileData: User | null;
} | null> => {
  try {
    const { data: { user: authUser } } = await supabase.auth.getUser();
    
    if (!authUser) return null;
    
    try {
      // Try to get user profile from the users table
      const { data: profileData, error } = await supabase
        .from('users')
        .select('*')
        .eq('user_id', authUser.id)
        .single();
      
      if (!error && profileData) {
        return { authUser, profileData };
      }
    } catch (profileError) {
      console.error('Error fetching user profile:', profileError);
    }
    
    // If we couldn't get the profile from the database, create a mock profile from auth metadata
    const userData = authUser.user_metadata || {};
    
    const mockProfile: User = {
      user_id: authUser.id,
      email: authUser.email || '',
      first_name: userData.first_name || '',
      last_name: userData.last_name || '',
      role: 'owner',
      created_at: authUser.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    return { authUser, profileData: mockProfile };
  } catch (error) {
    console.error('Error in getCurrentUser:', error);
    return null;
  }
};
