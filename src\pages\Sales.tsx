import React from 'react';
import { Search, Plus } from 'lucide-react';

export function Sales() {
  const services = [
    { id: 1, name: 'Haircut & Style', price: 60 },
    { id: 2, name: 'Color Treatment', price: 120 },
    { id: 3, name: 'Manicure', price: 40 },
    { id: 4, name: 'Pedicure', price: 50 },
  ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900">New Sale</h1>
      </div>

      <div className="bg-white shadow sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-6">
            <div>
              <label htmlFor="client" className="block text-sm font-medium text-gray-700">
                Client
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <input
                  type="text"
                  name="client"
                  id="client"
                  className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
                  placeholder="Search or add client"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center">
                <label className="block text-sm font-medium text-gray-700">
                  Services
                </label>
                <button className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                  <Plus className="h-4 w-4 mr-1" />
                  Add Service
                </button>
              </div>
              <div className="mt-2 border rounded-md divide-y">
                {services.map((service) => (
                  <div key={service.id} className="p-4 flex justify-between items-center hover:bg-gray-50">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{service.name}</p>
                    </div>
                    <div className="flex items-center">
                      <span className="text-sm text-gray-500">${service.price}</span>
                      <button className="ml-4 text-sm font-medium text-indigo-600 hover:text-indigo-500">
                        Add
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="border-t pt-4">
              <div className="flex justify-between items-center text-sm">
                <span className="font-medium text-gray-500">Subtotal</span>
                <span className="font-medium text-gray-900">$0.00</span>
              </div>
              <div className="flex justify-between items-center mt-2 text-sm">
                <span className="font-medium text-gray-500">Tax</span>
                <span className="font-medium text-gray-900">$0.00</span>
              </div>
              <div className="flex justify-between items-center mt-4 text-lg font-bold">
                <span className="text-gray-900">Total</span>
                <span className="text-indigo-600">$0.00</span>
              </div>
            </div>

            <div>
              <button className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Complete Sale
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}