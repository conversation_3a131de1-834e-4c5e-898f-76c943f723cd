import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

const BOT_URL = "https://wb-userbot-production.up.railway.app";

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { salonId } = await req.json();
    
    if (!salonId) {
      throw new Error("salonId is required");
    }

    console.log(`🔄 Resetting WhatsApp session for salon ${salonId}`);

    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    );

    // Step 1: Clear session on bot
    console.log("🧹 Clearing bot session...");
    try {
      const clearResponse = await fetch(`${BOT_URL}/session/${salonId}/clear`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });
      
      if (clearResponse.ok) {
        console.log("✅ Bot session cleared");
      } else {
        console.log("⚠️ Bot session clear failed (may not exist)");
      }
    } catch (error) {
      console.log("⚠️ Bot session clear error (expected):", error.message);
    }

    // Step 2: Clear auth_state from database
    console.log("🗄️ Clearing auth_state from database...");
    const { error: updateError } = await supabaseClient
      .from("salons")
      .update({ auth_state: null })
      .eq("salon_id", salonId);

    if (updateError) {
      throw new Error(`Failed to clear auth_state: ${updateError.message}`);
    }

    console.log("✅ Auth state cleared from database");

    // Step 3: Wait a moment for cleanup
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Step 4: Initialize fresh session
    console.log("🆕 Initializing fresh session...");
    const initResponse = await fetch(`${BOT_URL}/session/${salonId}/init`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const initResult = await initResponse.json();

    if (!initResponse.ok) {
      throw new Error(`Failed to initialize session: ${initResult.message}`);
    }

    console.log("✅ Fresh session initialized");

    return new Response(
      JSON.stringify({
        success: true,
        message: "WhatsApp session reset successfully",
        salon_id: salonId,
        next_steps: [
          "1. Go to WhatsApp settings on your phone",
          "2. Navigate to Linked Devices",
          "3. Log out of all existing sessions",
          "4. Scan the new QR code in your app settings"
        ],
        init_result: initResult,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      },
    );
  } catch (error) {
    console.error("Error resetting WhatsApp session:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      },
    );
  }
});
