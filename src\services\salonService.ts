import { supabase } from '../contexts/SimpleAuthContext';

// Services
export interface Service {
  service_id: string;
  salon_id: string;
  name: string;
  description: string | null;
  price: number;
  duration: number | null;
  created_at: string;
  updated_at: string;
}

export const getServices = async (salonId: string, includeInactive: boolean = false): Promise<Service[]> => {
  try {
    let query = supabase
      .from('services')
      .select('*')
      .eq('salon_id', salonId);

    // Only include active services unless specifically requested
    if (!includeInactive) {
      query = query.eq('is_active', true);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching services:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getServices:', error);
    return [];
  }
};

export const addService = async (service: Omit<Service, 'service_id' | 'created_at' | 'updated_at' | 'is_active'>): Promise<Service | null> => {
  try {
    const { data, error } = await supabase
      .from('services')
      .insert([{
        ...service,
        is_active: true, // Set as active by default
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }])
      .select()
      .single();

    if (error) {
      console.error('Error adding service:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in addService:', error);
    return null;
  }
};

export const updateService = async (serviceId: string, updates: Partial<Omit<Service, 'service_id' | 'salon_id' | 'created_at' | 'updated_at'>>): Promise<Service | null> => {
  try {
    const { data, error } = await supabase
      .from('services')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('service_id', serviceId)
      .select()
      .single();

    if (error) {
      console.error('Error updating service:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in updateService:', error);
    return null;
  }
};

export const deactivateService = async (serviceId: string): Promise<Service | null> => {
  try {
    const { data, error } = await supabase
      .from('services')
      .update({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('service_id', serviceId)
      .select()
      .single();

    if (error) {
      console.error('Error deactivating service:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in deactivateService:', error);
    return null;
  }
};

export const activateService = async (serviceId: string): Promise<Service | null> => {
  try {
    const { data, error } = await supabase
      .from('services')
      .update({
        is_active: true,
        updated_at: new Date().toISOString(),
      })
      .eq('service_id', serviceId)
      .select()
      .single();

    if (error) {
      console.error('Error activating service:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in activateService:', error);
    return null;
  }
};

// Keep this for backward compatibility, but it should not be used
export const deleteService = async (serviceId: string): Promise<boolean> => {
  console.warn('deleteService is deprecated. Use deactivateService instead.');
  const service = await deactivateService(serviceId);
  return service !== null;
};

// Staff (Users)
export interface StaffMember {
  user_id: string;
  salon_id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string | null;
  role: string;
  created_at: string;
  updated_at: string;
  display_name?: string;
  avatar_url?: string;
  is_active?: boolean;
}

export const getStaff = async (salonId: string, includeInactive: boolean = false): Promise<StaffMember[]> => {
  try {
    let query = supabase
      .from('users')
      .select('*')
      .eq('salon_id', salonId);

    // Only include active staff unless specifically requested
    if (!includeInactive) {
      query = query.eq('is_active', true);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching staff:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getStaff:', error);
    return [];
  }
};

export const addStaffMember = async (staffMember: Omit<StaffMember, 'user_id' | 'created_at' | 'updated_at' | 'is_active'>): Promise<StaffMember | null> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .insert([{
        ...staffMember,
        is_active: true, // Set as active by default
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }])
      .select()
      .single();

    if (error) {
      console.error('Error adding staff member:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in addStaffMember:', error);
    return null;
  }
};

export const updateStaffMember = async (userId: string, updates: Partial<Omit<StaffMember, 'user_id' | 'salon_id' | 'created_at' | 'updated_at'>>): Promise<StaffMember | null> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating staff member:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in updateStaffMember:', error);
    return null;
  }
};

export const deactivateStaffMember = async (userId: string): Promise<StaffMember | null> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .update({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error deactivating staff member:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in deactivateStaffMember:', error);
    return null;
  }
};

export const activateStaffMember = async (userId: string): Promise<StaffMember | null> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .update({
        is_active: true,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error activating staff member:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in activateStaffMember:', error);
    return null;
  }
};

// Keep this for backward compatibility, but it should not be used
export const deleteStaffMember = async (userId: string): Promise<boolean> => {
  console.warn('deleteStaffMember is deprecated. Use deactivateStaffMember instead.');
  const staff = await deactivateStaffMember(userId);
  return staff !== null;
};

// Clients
export interface Client {
  client_id: string;
  salon_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  email: string | null;
  address: string | null;
  created_at: string;
  updated_at: string;
}

export const getClients = async (salonId: string, includeInactive: boolean = false): Promise<Client[]> => {
  try {
    let query = supabase
      .from('clients')
      .select('*')
      .eq('salon_id', salonId);

    // Only include active clients unless specifically requested
    if (!includeInactive) {
      query = query.eq('is_active', true);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching clients:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getClients:', error);
    return [];
  }
};

export const addClient = async (client: Omit<Client, 'client_id' | 'created_at' | 'updated_at' | 'is_active'>): Promise<Client | null> => {
  try {
    const { data, error } = await supabase
      .from('clients')
      .insert([{
        ...client,
        is_active: true, // Set as active by default
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }])
      .select()
      .single();

    if (error) {
      console.error('Error adding client:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in addClient:', error);
    return null;
  }
};

export const updateClient = async (clientId: string, updates: Partial<Omit<Client, 'client_id' | 'salon_id' | 'created_at' | 'updated_at'>>): Promise<Client | null> => {
  try {
    const { data, error } = await supabase
      .from('clients')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('client_id', clientId)
      .select()
      .single();

    if (error) {
      console.error('Error updating client:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in updateClient:', error);
    return null;
  }
};

export const deactivateClient = async (clientId: string): Promise<Client | null> => {
  try {
    const { data, error } = await supabase
      .from('clients')
      .update({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('client_id', clientId)
      .select()
      .single();

    if (error) {
      console.error('Error deactivating client:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in deactivateClient:', error);
    return null;
  }
};

export const activateClient = async (clientId: string): Promise<Client | null> => {
  try {
    const { data, error } = await supabase
      .from('clients')
      .update({
        is_active: true,
        updated_at: new Date().toISOString(),
      })
      .eq('client_id', clientId)
      .select()
      .single();

    if (error) {
      console.error('Error activating client:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in activateClient:', error);
    return null;
  }
};

// Keep this for backward compatibility, but it should not be used
export const deleteClient = async (clientId: string): Promise<boolean> => {
  console.warn('deleteClient is deprecated. Use deactivateClient instead.');
  const client = await deactivateClient(clientId);
  return client !== null;
};

// Helper function to get the current user's salon ID
export const getCurrentUserSalonId = async (): Promise<string | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) return null;

    const { data, error } = await supabase
      .from('users')
      .select('salon_id')
      .eq('user_id', user.id)
      .single();

    if (error || !data) {
      console.error('Error getting salon ID:', error);
      return null;
    }

    return data.salon_id;
  } catch (error) {
    console.error('Error in getCurrentUserSalonId:', error);
    return null;
  }
};
