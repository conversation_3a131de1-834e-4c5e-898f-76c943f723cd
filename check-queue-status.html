<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WhatsApp Queue Status Checker</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      h1 {
        color: #333;
        text-align: center;
      }
      .button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
        font-size: 14px;
      }
      .button:hover {
        background-color: #0056b3;
      }
      .button.success {
        background-color: #28a745;
      }
      .button.danger {
        background-color: #dc3545;
      }
      .result {
        margin-top: 10px;
        padding: 10px;
        border-radius: 4px;
        white-space: pre-wrap;
        font-family: monospace;
        font-size: 12px;
        max-height: 400px;
        overflow-y: auto;
      }
      .result.success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
      }
      .result.error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
      }
      .result.info {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
      }
      th,
      td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }
      th {
        background-color: #f2f2f2;
      }
      .status-pending {
        background-color: #fff3cd;
      }
      .status-sent {
        background-color: #d4edda;
      }
      .status-failed {
        background-color: #f8d7da;
      }
      .status-auth_required {
        background-color: #ffeaa7;
      }
      .status-cancelled {
        background-color: #e2e3e5;
      }
    </style>
  </head>
  <body>
    <h1>WhatsApp Queue Status Checker</h1>

    <div class="container">
      <h2>Queue Operations</h2>
      <button class="button" onclick="checkQueueStatus()">
        Check Queue Status
      </button>
      <button class="button success" onclick="processQueue()">
        Process Queue Manually
      </button>
      <button class="button" onclick="checkCronStatus()">
        Check Cron Job Status
      </button>
      <button class="button danger" onclick="clearQueue()">
        Clear All Pending Messages
      </button>
      <div id="queueResult" class="result"></div>
    </div>

    <div class="container">
      <h2>Queue Messages</h2>
      <button class="button" onclick="loadQueueMessages()">
        Load Queue Messages
      </button>
      <div id="messagesResult" class="result"></div>
    </div>

    <script>
      const SUPABASE_URL = "https://mixjfinrxzpplzqidlas.supabase.co";
      const SUPABASE_ANON_KEY =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1peGpmaW5yeHpwcGx6cWlkbGFzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODY0MzgsImV4cCI6MjA1OTE2MjQzOH0.g4lTxBEZbG_GJKy_WrBJwW0H2tr9XaZHLzXpjdtbCzA";

      async function checkQueueStatus() {
        const resultDiv = document.getElementById("queueResult");
        resultDiv.textContent = "Checking queue status...";
        resultDiv.className = "result info";

        try {
          // Get queue statistics
          const response = await fetch(
            `${SUPABASE_URL}/rest/v1/whatsapp_message_queue?select=status,count()`,
            {
              headers: {
                apikey: SUPABASE_ANON_KEY,
                Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
                "Content-Type": "application/json",
              },
            }
          );

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();

          // Count by status
          const statusCounts = {};
          data.forEach((item) => {
            statusCounts[item.status] = (statusCounts[item.status] || 0) + 1;
          });

          resultDiv.className = "result success";
          resultDiv.textContent = `Queue Status Summary:\n${JSON.stringify(
            statusCounts,
            null,
            2
          )}`;
        } catch (error) {
          resultDiv.className = "result error";
          resultDiv.textContent = `Error: ${error.message}`;
        }
      }

      async function processQueue() {
        const resultDiv = document.getElementById("queueResult");
        resultDiv.textContent = "Processing queue manually...";
        resultDiv.className = "result info";

        try {
          const response = await fetch(
            `${SUPABASE_URL}/functions/v1/manual-queue-processor`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
            }
          );

          const result = await response.json();

          if (response.ok) {
            resultDiv.className = "result success";
            resultDiv.textContent = `Queue processing completed:\n${JSON.stringify(
              result,
              null,
              2
            )}`;
          } else {
            resultDiv.className = "result error";
            resultDiv.textContent = `Queue processing failed:\n${JSON.stringify(
              result,
              null,
              2
            )}`;
          }
        } catch (error) {
          resultDiv.className = "result error";
          resultDiv.textContent = `Error: ${error.message}`;
        }
      }

      async function checkCronStatus() {
        const resultDiv = document.getElementById("queueResult");
        resultDiv.textContent = "Checking cron job status...";
        resultDiv.className = "result info";

        try {
          // Check if pg_cron extension is enabled and get job status
          const response = await fetch(
            `${SUPABASE_URL}/rest/v1/rpc/check_cron_status`,
            {
              method: "POST",
              headers: {
                apikey: SUPABASE_ANON_KEY,
                Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
                "Content-Type": "application/json",
              },
            }
          );

          if (response.ok) {
            const result = await response.json();
            resultDiv.className = "result success";
            resultDiv.textContent = `Cron Status:\n${JSON.stringify(
              result,
              null,
              2
            )}`;
          } else {
            // Fallback: try to get cron job info directly
            const cronResponse = await fetch(
              `${SUPABASE_URL}/rest/v1/cron.job?select=*`,
              {
                headers: {
                  apikey: SUPABASE_ANON_KEY,
                  Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
                  "Content-Type": "application/json",
                },
              }
            );

            if (cronResponse.ok) {
              const cronJobs = await cronResponse.json();
              const whatsappJob = cronJobs.find(
                (job) => job.jobname === "whatsapp-queue-processor"
              );

              resultDiv.className = "result success";
              resultDiv.textContent = `Cron Job Status:\n${JSON.stringify(
                {
                  found: !!whatsappJob,
                  job: whatsappJob || "Not found",
                  total_jobs: cronJobs.length,
                },
                null,
                2
              )}`;
            } else {
              throw new Error(
                `HTTP ${response.status}: ${response.statusText}`
              );
            }
          }
        } catch (error) {
          resultDiv.className = "result error";
          resultDiv.textContent = `Error checking cron status: ${error.message}\n\nNote: Cron job status might not be accessible via REST API. Check Supabase dashboard for pg_cron extension status.`;
        }
      }

      async function clearQueue() {
        if (
          !confirm(
            "Are you sure you want to clear all pending messages? This cannot be undone."
          )
        ) {
          return;
        }

        const resultDiv = document.getElementById("queueResult");
        resultDiv.textContent = "Clearing pending messages...";
        resultDiv.className = "result info";

        try {
          const response = await fetch(
            `${SUPABASE_URL}/rest/v1/whatsapp_message_queue?status=eq.pending`,
            {
              method: "DELETE",
              headers: {
                apikey: SUPABASE_ANON_KEY,
                Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
                "Content-Type": "application/json",
              },
            }
          );

          if (response.ok) {
            resultDiv.className = "result success";
            resultDiv.textContent = "All pending messages cleared successfully";
          } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
        } catch (error) {
          resultDiv.className = "result error";
          resultDiv.textContent = `Error: ${error.message}`;
        }
      }

      async function loadQueueMessages() {
        const resultDiv = document.getElementById("messagesResult");
        resultDiv.textContent = "Loading queue messages...";
        resultDiv.className = "result info";

        try {
          const response = await fetch(
            `${SUPABASE_URL}/rest/v1/whatsapp_message_queue?select=*&order=created_at.desc&limit=50`,
            {
              headers: {
                apikey: SUPABASE_ANON_KEY,
                Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
                "Content-Type": "application/json",
              },
            }
          );

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const messages = await response.json();

          if (messages.length === 0) {
            resultDiv.className = "result info";
            resultDiv.textContent = "No messages in queue";
            return;
          }

          // Create table
          let tableHTML = `
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Salon ID</th>
                                <th>Phone</th>
                                <th>Status</th>
                                <th>Type</th>
                                <th>Retry Count</th>
                                <th>Created</th>
                                <th>Next Retry</th>
                                <th>Error</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

          messages.forEach((msg) => {
            const createdAt = new Date(msg.created_at).toLocaleString();
            const nextRetry = msg.next_retry_at
              ? new Date(msg.next_retry_at).toLocaleString()
              : "N/A";

            tableHTML += `
                        <tr class="status-${msg.status}">
                            <td>${msg.id}</td>
                            <td>${msg.salon_id}</td>
                            <td>${msg.phone_number}</td>
                            <td>${msg.status}</td>
                            <td>${msg.message_type}</td>
                            <td>${msg.retry_count}/${msg.max_retries}</td>
                            <td>${createdAt}</td>
                            <td>${nextRetry}</td>
                            <td>${msg.error_message || "None"}</td>
                        </tr>
                    `;
          });

          tableHTML += "</tbody></table>";

          resultDiv.className = "result success";
          resultDiv.innerHTML = `Found ${messages.length} messages in queue:${tableHTML}`;
        } catch (error) {
          resultDiv.className = "result error";
          resultDiv.textContent = `Error: ${error.message}`;
        }
      }

      // Auto-load queue status on page load
      window.onload = function () {
        checkQueueStatus();
        loadQueueMessages();
      };
    </script>
  </body>
</html>
