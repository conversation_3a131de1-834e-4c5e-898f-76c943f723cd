import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '../lib/supabase.config';
import type { User } from '../types/database';

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  authUser: any;
  loading: boolean;
  login: (email: string, password: string) => Promise<{ error?: any }>;
  signup: (email: string, password: string, userData: any) => Promise<{ error?: any, user?: any }>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error?: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<User | null>(null);
  const [authUser, setAuthUser] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Check for existing session and set up auth state change listener
  useEffect(() => {
    console.log('Setting up auth state...');

    // Function to get user profile data
    const getUserProfile = async (userId: string) => {
      try {
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('user_id', userId)
          .single();

        if (error) {
          console.error('Error fetching user profile:', error);
          return null;
        }

        return data;
      } catch (error) {
        console.error('Error in getUserProfile:', error);
        return null;
      }
    };

    // Add a safety timeout for the initial loading state
    const loadingTimeout = setTimeout(() => {
      if (loading) {
        console.log('Initial loading timeout reached, forcing loading state to false');
        setLoading(false);
      }
    }, 5000); // 5 second timeout

    // Check for existing session
    const checkSession = async () => {
      try {
        setLoading(true);

        // Get current session
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting session:', error);
          setIsAuthenticated(false);
          setAuthUser(null);
          setUser(null);
          setLoading(false);
          return;
        }

        if (session) {
          console.log('Session found:', session.user.id);
          setAuthUser(session.user);

          // Get user profile
          const profile = await getUserProfile(session.user.id);
          setUser(profile);
          setIsAuthenticated(true);
        } else {
          console.log('No session found');
          setIsAuthenticated(false);
          setAuthUser(null);
          setUser(null);
        }
      } catch (error) {
        console.error('Error checking session:', error);
        setIsAuthenticated(false);
        setAuthUser(null);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    // Initial session check
    checkSession();

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);

        // Set loading to true while processing auth state change
        setLoading(true);

        try {
          if (session) {
            console.log('Session found in auth state change');
            setAuthUser(session.user);

            // Get user profile
            try {
              const profile = await getUserProfile(session.user.id);
              setUser(profile);
            } catch (profileError) {
              console.error('Error getting user profile:', profileError);
              // Continue even if we can't get the profile
            }

            setIsAuthenticated(true);
            console.log('Auth state updated: isAuthenticated = true');

            // If this is a SIGNED_IN event, navigate to dashboard
            if (event === 'SIGNED_IN') {
              console.log('SIGNED_IN event detected, redirecting to dashboard');
              // Add a small delay to ensure state updates have propagated
              setTimeout(() => {
                console.log('Executing delayed navigation to dashboard');
                window.location.href = '/dashboard';
              }, 100);
              return; // Skip setting loading to false as we're redirecting
            }
          } else {
            console.log('No session found in auth state change');
            setIsAuthenticated(false);
            setAuthUser(null);
            setUser(null);
          }
        } catch (error) {
          console.error('Error in auth state change handler:', error);
          // Reset auth state on error
          setIsAuthenticated(false);
          setAuthUser(null);
          setUser(null);
        } finally {
          // Set loading to false unless we're redirecting
          if (!(event === 'SIGNED_IN' && session)) {
            console.log('Setting loading to false in auth state change');
            setLoading(false);
          }
        }
      }
    );

    // Clean up subscription and timeout
    return () => {
      subscription.unsubscribe();
      clearTimeout(loadingTimeout);
    };
  }, []);

  // Login function
  const login = async (email: string, password: string) => {
    try {
      console.log('Attempting to login with email:', email);
      setLoading(true);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Login error:', error);
        setLoading(false); // Reset loading state on error
        return { error };
      }

      console.log('Login successful, session created');

      // Add a safety timeout to ensure loading state is reset if auth state change listener doesn't fire
      setTimeout(() => {
        if (loading) {
          console.log('Safety timeout: resetting loading state and redirecting');
          setLoading(false);
          window.location.href = '/dashboard';
        }
      }, 3000); // 3 second timeout

      // Note: We don't set loading to false here because the auth state change listener
      // will handle the redirect to the dashboard

      return { error: null };
    } catch (error) {
      console.error('Login error:', error);
      setLoading(false); // Reset loading state on error
      return { error };
    }
    // Note: We don't have a finally block with setLoading(false) here because
    // the auth state change listener will handle that
  };

  // Signup function
  const signup = async (email: string, password: string, userData: any) => {
    try {
      setLoading(true);

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData,
        },
      });

      if (error) {
        console.error('Signup error:', error);
        return { error };
      }

      return { error: null, user: data.user };
    } catch (error) {
      console.error('Signup error:', error);
      return { error };
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setLoading(true);

      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('Logout error:', error);
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Reset password function
  const resetPassword = async (email: string) => {
    try {
      setLoading(true);

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        console.error('Reset password error:', error);
        return { error };
      }

      return { error: null };
    } catch (error) {
      console.error('Reset password error:', error);
      return { error };
    } finally {
      setLoading(false);
    }
  };

  const value = {
    isAuthenticated,
    user,
    authUser,
    loading,
    login,
    signup,
    logout,
    resetPassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
