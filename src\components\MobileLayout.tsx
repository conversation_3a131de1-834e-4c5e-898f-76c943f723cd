import React from 'react';
import { Outlet } from 'react-router-dom';
import { MobileNavBar } from './MobileNavBar';
import { MobileHeader } from './MobileHeader';

export function MobileLayout() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-50/30 to-purple-50/30 flex flex-col">
      <MobileHeader />
      <main className="flex-1 px-4 py-4 pb-20 overflow-x-hidden">
        <Outlet />
      </main>
      <MobileNavBar />
    </div>
  );
}
