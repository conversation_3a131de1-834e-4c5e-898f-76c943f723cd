import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/SimpleAuthContext';

export function DebugPanel() {
  const { isAuthenticated, loading, user } = useAuth();
  const [showDebug, setShowDebug] = useState(false);
  const [loadingHistory, setLoadingHistory] = useState<string[]>([]);
  const navigate = useNavigate();

  // Track loading state changes
  useEffect(() => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    setLoadingHistory(prev => [...prev, `${timestamp}: loading=${loading}`].slice(-10));
  }, [loading]);

  if (!showDebug) {
    return (
      <button
        onClick={() => setShowDebug(true)}
        className="fixed bottom-4 right-4 bg-gray-800 text-white p-2 rounded-full opacity-50 hover:opacity-100"
      >
        Debug
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 shadow-lg p-4 rounded-lg w-80 z-50">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">Debug Panel</h3>
        <button
          onClick={() => setShowDebug(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          Close
        </button>
      </div>

      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span>isAuthenticated:</span>
          <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>
            {isAuthenticated ? 'true' : 'false'}
          </span>
        </div>

        <div className="flex justify-between">
          <span>loading:</span>
          <span className={loading ? 'text-yellow-600' : 'text-green-600'}>
            {loading ? 'true' : 'false'}
          </span>
        </div>



        <div className="flex justify-between">
          <span>user:</span>
          <span>{user ? user.email : 'null'}</span>
        </div>

        <div className="mt-2">
          <p className="font-medium mb-1">Loading History:</p>
          <div className="bg-gray-100 p-2 rounded text-xs">
            {loadingHistory.map((entry, i) => (
              <div key={i}>{entry}</div>
            ))}
          </div>
        </div>

        <div className="mt-2 flex space-x-2">
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-3 py-1 rounded text-xs"
          >
            Reload Page
          </button>

          <button
            onClick={() => {
              // Force a hard navigation to the dashboard
              window.location.href = '/dashboard';
            }}
            className="bg-green-500 text-white px-3 py-1 rounded text-xs"
          >
            Go to Dashboard
          </button>

          <button
            onClick={() => {
              // Force redirect to auth page
              window.location.href = '/auth';
            }}
            className="bg-red-500 text-white px-3 py-1 rounded text-xs"
          >
            Go to Login
          </button>
        </div>
      </div>
    </div>
  );
}
